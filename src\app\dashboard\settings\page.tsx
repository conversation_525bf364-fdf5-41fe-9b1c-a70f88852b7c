"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { useSession } from "next-auth/react"
import Link from "next/link"
import { useUserSettings } from "@/hooks/useUserSettings"
import {
  MessageSquare,
  Calendar,
  AlertCircle,
  CheckCircle,
  Loader2,
  RefreshCw,
  Mail,
  Crown,
  Zap,
  TrendingUp,
} from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("usage")
  const { data: session } = useSession()
  const {
    plan,
    usage,
    isLoading,
    error,
    fetchUserSettings,
    refreshUsage,
    canCreateWidget,
    canSendMessage,
    remainingMessages,
    remainingWidgets,
    usagePercentage,
  } = useUserSettings()

  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefreshUsage = async () => {
    setIsRefreshing(true)
    await refreshUsage()
    setIsRefreshing(false)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4 md:p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-3xl shadow-xl border border-slate-200 p-8">
            <div className="space-y-6">
              <div className="space-y-2">
                <div className="h-8 bg-slate-200 rounded-lg animate-pulse w-48"></div>
                <div className="h-4 bg-slate-100 rounded animate-pulse w-64"></div>
              </div>
              <div className="flex items-center justify-center py-16">
                <div className="flex flex-col items-center space-y-4">
                  <Loader2 className="w-12 h-12 animate-spin text-indigo-600" />
                  <p className="text-slate-600 font-medium">Loading your settings...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4 md:p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-3xl shadow-xl border border-slate-200 p-8">
            <header className="mb-8">
              <h1 className="text-4xl font-bold text-slate-900 mb-2">Settings</h1>
              <p className="text-slate-600 text-lg">Manage your plan and usage</p>
            </header>
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <AlertDescription className="text-red-800">
                <span className="font-medium">Error loading settings:</span> {error}
                <Button
                  variant="outline"
                  size="sm"
                  className="ml-3 border-red-300 text-red-700 hover:bg-red-100"
                  onClick={fetchUserSettings}
                >
                  <RefreshCw className="w-4 h-4 mr-1" />
                  Retry
                </Button>
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4 md:p-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-3xl shadow-xl border border-slate-200 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 px-8 py-12">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h1 className="text-4xl md:text-5xl font-bold text-white mb-3">Settings</h1>
                <p className="text-slate-300 text-lg">Manage your plan and monitor usage</p>
              </div>
              <div className="mt-6 md:mt-0">
                <Button
                  onClick={handleRefreshUsage}
                  disabled={isRefreshing}
                  className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-xl font-medium shadow-lg transition-all duration-200 hover:shadow-xl"
                >
                  {isRefreshing ? (
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  ) : (
                    <RefreshCw className="w-5 h-5 mr-2" />
                  )}
                  Refresh Data
                </Button>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="border-b border-slate-200 bg-slate-50">
            <div className="px-8">
              <nav className="flex space-x-8">
                <button
                  onClick={() => setActiveTab("usage")}
                  className={`py-6 border-b-3 font-semibold text-sm transition-all duration-200 flex items-center ${
                    activeTab === "usage"
                      ? "border-indigo-600 text-indigo-600"
                      : "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300"
                  }`}
                >
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Usage Overview
                </button>
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="p-8">
            {activeTab === "usage" && (
              <div className="space-y-8">
                {/* Current Plan Card */}
                <Card className="relative overflow-hidden border-0 shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-600 via-indigo-700 to-purple-700"></div>
                  <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fillRule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fillOpacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"2\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
                  <div className="relative p-8">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-4">
                          <Crown className="w-8 h-8 text-yellow-300" />
                          <h3 className="text-2xl font-bold text-white">Current Plan</h3>
                        </div>

                        <div className="flex flex-wrap items-center gap-3 mb-6">
                          <Badge className={`px-4 py-2 text-sm font-semibold rounded-full ${
                            plan?.plan === "pro"
                              ? "bg-yellow-400 text-yellow-900"
                              : "bg-white/20 text-white border border-white/30"
                          }`}>
                            {plan?.plan === "pro" ? "✨ Pro Plan" : "🚀 Free Plan"}
                          </Badge>
                          <Badge className="bg-green-400 text-green-900 px-4 py-2 text-sm font-semibold rounded-full">
                            ✓ {plan?.status || "Active"}
                          </Badge>
                        </div>

                        <p className="text-white/90 text-lg mb-4 leading-relaxed">
                          {plan?.plan === "pro"
                            ? `Enjoy unlimited access with ${usage?.limits.messages} AI responses and ${usage?.limits.widgets} widgets monthly.`
                            : `Get started with ${usage?.limits.messages} AI responses and ${usage?.limits.widgets} widget on your free account.`}
                        </p>

                        {plan?.currentPeriodEnd && (
                          <div className="flex items-center text-white/80 mb-6">
                            <Calendar className="w-5 h-5 mr-2" />
                            <span className="text-sm">
                              Renews on {new Date(plan.currentPeriodEnd).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                              })}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex flex-col sm:flex-row gap-3 mt-6 lg:mt-0">
                        <Link href="/dashboard/credits">
                          <Button className="bg-white text-indigo-700 hover:bg-slate-50 px-6 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200 hover:shadow-xl">
                            <Zap className="w-5 h-5 mr-2" />
                            {plan?.plan === "pro" ? "Manage Plan" : "Upgrade Now"}
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Usage Statistics Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* AI Responses Card */}
                  <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-slate-50 overflow-hidden">
                    <div className="p-8">
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center space-x-3">
                          <div className="p-3 bg-blue-100 rounded-2xl">
                            <MessageSquare className="w-6 h-6 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-slate-900">AI Responses</h3>
                            <p className="text-slate-500 text-sm">Monthly usage</p>
                          </div>
                        </div>
                        <Badge className={`px-3 py-1 rounded-full font-medium ${
                          canSendMessage
                            ? "bg-green-100 text-green-700"
                            : "bg-red-100 text-red-700"
                        }`}>
                          {canSendMessage ? "Available" : "Limit Reached"}
                        </Badge>
                      </div>

                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-slate-600 font-medium">Usage Progress</span>
                          <span className="text-2xl font-bold text-slate-900">
                            {usage?.messageCount || 0}
                            <span className="text-lg text-slate-500 font-normal">
                              /{usage?.limits.messages || 0}
                            </span>
                          </span>
                        </div>

                        <div className="relative">
                          <Progress
                            value={usagePercentage.messages}
                            className="h-3 bg-slate-200"
                          />
                          <div 
                            className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full transition-all duration-300"
                            style={{ width: `${usagePercentage.messages}%` }}
                          ></div>
                        </div>

                        <div className="flex justify-between items-center pt-2">
                          <span className="text-slate-500 text-sm">Remaining</span>
                          <span className="text-lg font-bold text-green-600">
                            {remainingMessages}
                          </span>
                        </div>
                      </div>

                      {!canSendMessage && (
                        <Alert className="mt-6 border-orange-200 bg-orange-50">
                          <AlertCircle className="h-5 w-5 text-orange-600" />
                          <AlertDescription className="text-orange-800">
                            <span className="font-medium">Monthly limit reached.</span> Upgrade to continue using AI responses.
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  </Card>

                  {/* Widgets Card */}
                  <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-slate-50 overflow-hidden">
                    <div className="p-8">
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center space-x-3">
                          <div className="p-3 bg-purple-100 rounded-2xl">
                            <Mail className="w-6 h-6 text-purple-600" />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-slate-900">Widgets</h3>
                            <p className="text-slate-500 text-sm">Created widgets</p>
                          </div>
                        </div>
                        <Badge className={`px-3 py-1 rounded-full font-medium ${
                          canCreateWidget
                            ? "bg-green-100 text-green-700"
                            : "bg-red-100 text-red-700"
                        }`}>
                          {canCreateWidget ? "Available" : "Limit Reached"}
                        </Badge>
                      </div>

                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-slate-600 font-medium">Widgets Created</span>
                          <span className="text-2xl font-bold text-slate-900">
                            {usage?.widgetCount || 0}
                            <span className="text-lg text-slate-500 font-normal">
                              /{usage?.limits.widgets || 0}
                            </span>
                          </span>
                        </div>

                        <div className="relative">
                          <Progress
                            value={usagePercentage.widgets}
                            className="h-3 bg-slate-200"
                          />
                          <div 
                            className="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 to-pink-600 rounded-full transition-all duration-300"
                            style={{ width: `${usagePercentage.widgets}%` }}
                          ></div>
                        </div>

                        <div className="flex justify-between items-center pt-2">
                          <span className="text-slate-500 text-sm">Remaining</span>
                          <span className="text-lg font-bold text-green-600">
                            {remainingWidgets}
                          </span>
                        </div>
                      </div>

                      {!canCreateWidget && (
                        <Alert className="mt-6 border-orange-200 bg-orange-50">
                          <AlertCircle className="h-5 w-5 text-orange-600" />
                          <AlertDescription className="text-orange-800">
                            <span className="font-medium">Widget limit reached.</span> Upgrade to create more widgets.
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  </Card>
                </div>

                {/* Reset Info */}
                {usage?.resetDate && (
                  <Card className="border-0 shadow-lg bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-l-green-500">
                    <div className="p-6">
                      <div className="flex items-center space-x-3">
                        <CheckCircle className="w-6 h-6 text-green-600" />
                        <div>
                          <p className="text-green-800 font-medium">
                            Usage limits reset on {new Date(usage.resetDate).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </p>
                          <p className="text-green-600 text-sm">Your monthly quotas will be refreshed automatically</p>
                        </div>
                      </div>
                    </div>
                  </Card>
                )}

                {/* Upgrade Prompt for Free Users */}
                {plan?.plan === "free" && (
                  <Card className="border-0 shadow-2xl bg-gradient-to-br from-slate-900 to-slate-800 overflow-hidden">
                    <div className="p-8">
                      <div className="text-center mb-8">
                        <Crown className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
                        <h3 className="text-3xl font-bold text-white mb-2">Ready to Level Up?</h3>
                        <p className="text-slate-300 text-lg">Unlock the full potential of our platform</p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <div className="bg-slate-800/50 rounded-2xl p-6 border border-slate-700">
                          <h4 className="font-bold text-white mb-4 text-lg">Free Plan</h4>
                          <ul className="space-y-3 text-slate-300">
                            <li className="flex items-center">
                              <div className="w-2 h-2 bg-slate-500 rounded-full mr-3"></div>
                              20 AI responses/month
                            </li>
                            <li className="flex items-center">
                              <div className="w-2 h-2 bg-slate-500 rounded-full mr-3"></div>
                              1 widget
                            </li>
                            <li className="flex items-center">
                              <div className="w-2 h-2 bg-slate-500 rounded-full mr-3"></div>
                              Basic support
                            </li>
                          </ul>
                        </div>

                        <div className="bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl p-6 border border-indigo-500 relative">
                          <div className="absolute -top-3 -right-3 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold">
                            POPULAR
                          </div>
                          <h4 className="font-bold text-white mb-4 text-lg">Pro Plan</h4>
                          <ul className="space-y-3 text-white">
                            <li className="flex items-center">
                              <CheckCircle className="w-5 h-5 text-green-300 mr-3" />
                              1,000 AI responses/month
                            </li>
                            <li className="flex items-center">
                              <CheckCircle className="w-5 h-5 text-green-300 mr-3" />
                              10 widgets
                            </li>
                            <li className="flex items-center">
                              <CheckCircle className="w-5 h-5 text-green-300 mr-3" />
                              Priority support
                            </li>
                            <li className="flex items-center">
                              <CheckCircle className="w-5 h-5 text-green-300 mr-3" />
                              Advanced analytics
                            </li>
                          </ul>
                        </div>
                      </div>

                      <div className="text-center">
                        <Link href="/dashboard/credits">
                          <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-4 rounded-2xl font-bold text-lg shadow-2xl transition-all duration-200 hover:shadow-3xl transform hover:-translate-y-1">
                            <Crown className="w-6 h-6 mr-2" />
                            Upgrade to Pro
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </Card>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}