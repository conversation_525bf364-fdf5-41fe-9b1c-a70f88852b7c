.better-faq-widget {
    position: fixed;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.better-faq-widget-button {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--primary-color, #000000);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.better-faq-widget-button:hover {
    transform: scale(1.1);
}

.better-faq-widget-window {
    position: fixed;
    width: 380px;
    height: 600px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.better-faq-widget-header {
    padding: 16px;
    background: var(--primary-color, #000000);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.better-faq-widget-header-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.better-faq-widget-logo {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.better-faq-widget-logo-placeholder {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}

.better-faq-widget-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.better-faq-widget-header p {
    margin: 0;
    font-size: 12px;
    opacity: 0.8;
}

.better-faq-widget-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.better-faq-widget-close:hover {
    opacity: 1;
}

.better-faq-widget-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.better-faq-widget-message {
    display: flex;
    flex-direction: column;
    max-width: 80%;
}

.better-faq-widget-message-user {
    align-self: flex-end;
}

.better-faq-widget-message-assistant {
    align-self: flex-start;
}

.better-faq-widget-message-content {
    padding: 12px 16px;
    border-radius: 12px;
    background: var(--accent-color, #f5f5f5);
    color: var(--primary-color, #000000);
}

.better-faq-widget-message-user .better-faq-widget-message-content {
    background: var(--primary-color, #000000);
    color: white;
}

.better-faq-widget-message-content p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

.better-faq-widget-message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 4px;
    display: block;
}

.better-faq-widget-input {
    padding: 16px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 8px;
}

.better-faq-widget-input input {
    flex: 1;
    padding: 12px;
    border: 1px solid #eee;
    border-radius: 8px;
    outline: none;
    font-size: 14px;
    transition: border-color 0.2s;
}

.better-faq-widget-input input:focus {
    border-color: var(--primary-color, #000000);
}

.better-faq-widget-send {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--primary-color, #000000);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s;
}

.better-faq-widget-send:hover {
    opacity: 0.9;
}

.better-faq-widget-send:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}