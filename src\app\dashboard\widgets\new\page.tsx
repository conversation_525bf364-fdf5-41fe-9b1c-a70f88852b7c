"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useWidgetsStore } from "@/store/useWidgetsStore"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card"
import { Input } from "@/components/ui/Input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/Select"
import { toast } from "sonner"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import Widget from "@/components/Widget"

const widgetFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  position: z.enum(["bottom-right", "bottom-left", "top-right", "top-left"]),
  primaryColor: z.string().min(1, "Primary color is required"),
  accentColor: z.string().min(1, "Accent color is required"),
  companyName: z.string().optional(),
  companyLogo: z.string().optional(),
  welcomeMessage: z.string().optional(),
  commonQuestions: z.array(z.object({
    question: z.string(),
    answer: z.string()
  })),
  feedbackQuestion: z.string(),
  isActive: z.boolean()
})

type WidgetFormValues = z.infer<typeof widgetFormSchema>

export default function NewWidgetPage() {
  const router = useRouter()
  const { createWidget } = useWidgetsStore()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<WidgetFormValues>({
    resolver: zodResolver(widgetFormSchema),
    defaultValues: {
      name: "",
      position: "bottom-right",
      primaryColor: "#000000",
      accentColor: "#ffffff",
      companyName: "",
      companyLogo: "",
      welcomeMessage: "",
      commonQuestions: [],
      feedbackQuestion: "How was your experience?",
      isActive: true
    },
  })

  const onSubmit = async (data: WidgetFormValues) => {
    try {
      setIsSubmitting(true)
      await createWidget(data)
      toast.success("Widget created successfully")
      router.push("/dashboard/widget")
    } catch (error) {
      toast.error("Failed to create widget")
    } finally {
      setIsSubmitting(false)
    }
  }

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = form

  return (
    <div className="max-w-7xl mx-auto py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Create New Widget</CardTitle>
            <CardDescription>Customize your widget's appearance and behavior</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input id="name" placeholder="My Widget" {...register("name")} />
                {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
                <p className="text-sm text-muted-foreground">A name to identify your widget</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="position">Position</Label>
                <Select onValueChange={(value) => setValue("position", value as any)} defaultValue={watch("position")}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select position" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bottom-right">Bottom Right</SelectItem>
                    <SelectItem value="bottom-left">Bottom Left</SelectItem>
                    <SelectItem value="top-right">Top Right</SelectItem>
                    <SelectItem value="top-left">Top Left</SelectItem>
                  </SelectContent>
                </Select>
                {errors.position && <p className="text-sm text-red-500">{errors.position.message}</p>}
                <p className="text-sm text-muted-foreground">Where the widget will appear on the page</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="primaryColor">Primary Color</Label>
                <div className="flex gap-2">
                  <Input type="color" {...register("primaryColor")} className="w-12 h-10 p-1" />
                  <Input placeholder="#000000" {...register("primaryColor")} className="flex-1" />
                </div>
                {errors.primaryColor && <p className="text-sm text-red-500">{errors.primaryColor.message}</p>}
                <p className="text-sm text-muted-foreground">The main color of your widget</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="accentColor">Accent Color</Label>
                <div className="flex gap-2">
                  <Input type="color" {...register("accentColor")} className="w-12 h-10 p-1" />
                  <Input placeholder="#ffffff" {...register("accentColor")} className="flex-1" />
                </div>
                {errors.accentColor && <p className="text-sm text-red-500">{errors.accentColor.message}</p>}
                <p className="text-sm text-muted-foreground">The secondary color of your widget</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="companyName">Company Name</Label>
                <Input id="companyName" placeholder="Your Company" {...register("companyName")} />
                {errors.companyName && <p className="text-sm text-red-500">{errors.companyName.message}</p>}
                <p className="text-sm text-muted-foreground">Your company name (optional)</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="companyLogo">Company Logo URL</Label>
                <Input id="companyLogo" placeholder="https://example.com/logo.png" {...register("companyLogo")} />
                {errors.companyLogo && <p className="text-sm text-red-500">{errors.companyLogo.message}</p>}
                <p className="text-sm text-muted-foreground">URL to your company logo (optional)</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="welcomeMessage">Welcome Message</Label>
                <Input id="welcomeMessage" placeholder="How can I help you today?" {...register("welcomeMessage")} />
                {errors.welcomeMessage && <p className="text-sm text-red-500">{errors.welcomeMessage.message}</p>}
                <p className="text-sm text-muted-foreground">Message shown when the widget is opened (optional)</p>
              </div>

              <div className="space-y-2">
                <Label>Common Questions</Label>
                <div className="space-y-4">
                  {watch("commonQuestions").map((_, index) => (
                    <div key={index} className="space-y-2 p-4 border rounded-lg">
                      <div className="space-y-2">
                        <Label>Question {index + 1}</Label>
                        <Input
                          placeholder="Enter your question"
                          {...register(`commonQuestions.${index}.question`)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Answer {index + 1}</Label>
                        <Input
                          placeholder="Enter the answer"
                          {...register(`commonQuestions.${index}.answer`)}
                        />
                      </div>
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={() => {
                          const questions = watch("commonQuestions");
                          setValue(
                            "commonQuestions",
                            questions.filter((_, i) => i !== index)
                          );
                        }}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      const questions = watch("commonQuestions");
                      setValue("commonQuestions", [
                        ...questions,
                        { question: "", answer: "" },
                      ]);
                    }}
                  >
                    Add Question
                  </Button>
                </div>
                {errors.commonQuestions && (
                  <p className="text-sm text-red-500">{errors.commonQuestions.message}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  Add common questions and their answers
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="feedbackQuestion">Feedback Question</Label>
                <Input
                  id="feedbackQuestion"
                  placeholder="How was your experience?"
                  {...register("feedbackQuestion")}
                />
                {errors.feedbackQuestion && (
                  <p className="text-sm text-red-500">{errors.feedbackQuestion.message}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  Question shown when asking for feedback
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="isActive">Is Active</Label>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isActive"
                    {...register("isActive")}
                    className="h-4 w-4 rounded border-gray-300"
                  />
                  <span className="text-sm">Enable this widget</span>
                </div>
                {errors.isActive && (
                  <p className="text-sm text-red-500">{errors.isActive.message}</p>
                )}
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => router.push("/dashboard/widget")}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Creating..." : "Create Widget"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Preview</CardTitle>
            <CardDescription>See how your widget will look</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative h-[600px] border rounded-lg">
              <Widget
                widgetId="preview"
                position={watch("position")}
                primaryColor={watch("primaryColor")}
                accentColor={watch("accentColor")}
                companyName={watch("companyName")}
                companyLogo={watch("companyLogo")}
                welcomeMessage={watch("welcomeMessage")}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
