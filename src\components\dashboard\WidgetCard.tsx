import { Card } from "@/components/ui/Card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/Badge";
import { Co<PERSON>, Settings, Trash2 } from "lucide-react";
import { useWidgetsStore } from "@/store/useWidgetsStore";
import type { Widget } from "@/store/useWidgetsStore";
import { toast } from "sonner";

interface WidgetCardProps {
  widget: Widget;
  onClick: () => void;
}

export function WidgetCard({ widget, onClick }: WidgetCardProps) {
  const { deleteWidget } = useWidgetsStore();

  const copySnippet = () => {
    const snippet = `<script src="${
      process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3001"
    }/widget.js" data-widget-id="${widget.id}"></script>`;
    navigator.clipboard.writeText(snippet);
    toast.success("Code snippet copied to clipboard");
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm("Are you sure you want to delete this widget?")) {
      await deleteWidget(widget.id);
      toast.success("Widget deleted successfully");
    }
  };

  return (
    <Card
      className="relative group cursor-pointer hover:shadow-lg transition-all"
      onClick={onClick}
    >
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="font-semibold">{widget.name}</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Created {new Date(widget.createdAt).toLocaleDateString()}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => {
                e.stopPropagation();
                copySnippet();
              }}
            >
              <Copy className="h-4 w-4" />
            </Button>
            <Button
              variant="destructive"
              size="icon"
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={handleDelete}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>{" "}
        <div className="space-y-2">
          <div className="flex items-center text-sm">
            <span className="font-medium mr-2">Position:</span>
            {widget.position}
          </div>
          <div className="flex items-center text-sm gap-2">
            <span className="font-medium">Colors:</span>
            <span
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: widget.primaryColor }}
              title="Background"
            />
            <span
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: widget.accentColor }}
              title="Accent"
            />
          </div>
          <div className="flex items-center text-sm">
            <span className="font-medium mr-2">Questions:</span>
            {widget.commonQuestions?.length || 0}
          </div>
          <div className="flex items-center text-sm">
            <span className="font-medium mr-2">Status:</span>
            <Badge variant={widget.isActive ? "default" : "secondary"}>
              {widget.isActive ? "Active" : "Inactive"}
            </Badge>
          </div>
        </div>
      </div>
    </Card>
  );
}
