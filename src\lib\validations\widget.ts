import { z } from "zod";

export const widgetFormSchema = z.object({
  name: z.string().min(1, "Name is required").max(50, "Name must be less than 50 characters"),
  position: z.enum(["bottom-right", "bottom-left", "top-right", "top-left"]),
  primaryColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Invalid color format - use 6-digit hex code (#RRGGBB)"),
  accentColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Invalid color format - use 6-digit hex code (#RRGGBB)"),
  companyName: z.string().optional(),
  companyLogo: z.string().optional(),
  welcomeMessage: z.string().optional(),
  termsOfService: z.string().optional(),
  privacyPolicy: z.string().optional(),
  productDescription: z.string().optional(),
  features: z.array(z.string()).optional(),
  pricing: z.object({
    plans: z.array(z.object({
      name: z.string(),
      price: z.string(),
      features: z.array(z.string())
    }))
  }).optional(),
  commonQuestions: z.array(z.object({
    question: z.string().min(1, "Question is required").max(200, "Question must be less than 200 characters"),
    answer: z.string().min(1, "Answer is required").max(1000, "Answer must be less than 1000 characters"),
  })),
  feedbackQuestion: z.string().min(1, "Feedback question is required").max(200, "Feedback question must be less than 200 characters"),
  isActive: z.boolean(),
});

export type WidgetFormValues = z.infer<typeof widgetFormSchema>;
