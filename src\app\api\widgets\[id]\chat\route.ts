import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { analytics, widget } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';

// POST - Handle chat messages for a specific widget (public endpoint)
export async function POST(
    request: NextRequest,
    context: { params: Promise<{ id: string }> }
) {
    try {
        const params = await context.params;
        const body = await request.json();
        const { message } = body;

        // Validate required fields
        if (!message) {
            return NextResponse.json(
                { error: 'Message is required' },
                { status: 400 }
            );
        }

        // Verify widget exists and is active
        const widgetData = await db.query.widget.findFirst({
            where: eq(widget.id, params.id),
        });

        if (!widgetData) {
            return NextResponse.json(
                { error: 'Widget not found' },
                { status: 404 }
            );
        }

        if (!widgetData.isActive) {
            return NextResponse.json(
                { error: 'Widget is not active' },
                { status: 400 }
            );
        }

        // Generate AI response (replace with your actual AI logic)
        const aiResponse = await generateAIResponse(message, widgetData);

        // Generate a session ID for tracking
        const sessionId = uuidv4();

        // Log the interaction in analytics
        try {
            await db.insert(analytics).values({
                widgetId: params.id,
                eventType: 'message_sent',
                eventData: {
                    message,
                    response: aiResponse,
                    timestamp: new Date().toISOString(),
                },
                sessionId,
            });
        } catch (analyticsError) {
            console.error('Error logging analytics:', analyticsError);
            // Don't fail the request if analytics fails
        }

        return NextResponse.json({
            response: aiResponse,
            sessionId,
        }, {
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type',
            }
        });

    } catch (error) {
        console.error('Error processing widget chat message:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            {
                status: 500,
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type',
                }
            }
        );
    }
}

// Handle preflight requests
export async function OPTIONS(request: Request) {
    return new NextResponse(null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
        },
    });
}

// Helper function to generate AI response
async function generateAIResponse(message: string, widgetData: any): Promise<string> {
    // Check if the message matches any common questions
    if (widgetData.commonQuestions && widgetData.commonQuestions.length > 0) {
        for (const qa of widgetData.commonQuestions) {
            if (message.toLowerCase().includes(qa.question.toLowerCase()) || 
                qa.question.toLowerCase().includes(message.toLowerCase())) {
                return qa.answer;
            }
        }
    }

    // Default responses based on message content
    const responses = [
        "Thank you for your message! How can I help you today?",
        "I'm here to assist you. Could you please provide more details?",
        "That's a great question! Let me help you with that.",
        "I understand your concern. Here's what I can tell you...",
        "Thanks for reaching out! I'm happy to help.",
    ];

    // Simple keyword-based responses
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('pricing')) {
        return widgetData.pricing ? 
            "Here are our pricing options. Please check our pricing section for detailed information." :
            "For pricing information, please contact our sales team.";
    }
    
    if (lowerMessage.includes('feature') || lowerMessage.includes('what can') || lowerMessage.includes('capabilities')) {
        return widgetData.features && widgetData.features.length > 0 ?
            `Our main features include: ${widgetData.features.slice(0, 3).join(', ')}. Would you like to know more about any specific feature?` :
            "We offer a comprehensive set of features designed to meet your needs. Would you like to learn more?";
    }
    
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
        return widgetData.welcomeMessage || "Hello! How can I assist you today?";
    }
    
    if (lowerMessage.includes('help') || lowerMessage.includes('support')) {
        return "I'm here to help! Please let me know what specific assistance you need.";
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Return a random response
    return responses[Math.floor(Math.random() * responses.length)];
}
