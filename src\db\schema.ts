import {
  timestamp,
  pgTable,
  text,
  primaryKey,
  integer,
  serial,
  boolean,
  json,
  decimal,
  uuid,
} from 'drizzle-orm/pg-core';

export const user = pgTable('user', {
  id: uuid('id').defaultRandom().primaryKey(),
  email: text('email').notNull().unique(),
  emailVerified: timestamp('emailVerified', { mode: 'date' }),
  name: text('name'),
  image: text('image'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const widget = pgTable('widget', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => user.id).notNull(),
  name: text('name').notNull(),
  position: text('position').notNull().default('bottom-right'),
  primaryColor: text('primary_color').default('#4F46E5').notNull(),
  accentColor: text('accent_color').default('#ffffff').notNull(),
  companyName: text('company_name'),
  companyLogo: text('company_logo'),
  welcomeMessage: text('welcome_message'),
  termsOfService: text('terms_of_service'),
  privacyPolicy: text('privacy_policy'),
  productDescription: text('product_description'),
  features: json('features').$type<string[]>(),
  pricing: json('pricing').$type<{
    plans: Array<{
      name: string;
      price: string;
      features: string[];
    }>;
  }>(),
  commonQuestions: json('common_questions').$type<Array<{
    question: string;
    answer: string;
  }>>().default([]),
  feedbackQuestion: text('feedback_question').default('How was your experience?'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const widgetData = pgTable('widget_data', {
  id: uuid('id').defaultRandom().primaryKey(),
  widgetId: uuid('widget_id').references(() => widget.id).notNull(),
  type: text('type').notNull(), // 'faq', 'product_info', etc.
  content: json('content').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const feedback = pgTable('feedback', {
  id: uuid('id').defaultRandom().primaryKey(),
  widgetId: uuid('widget_id').references(() => widget.id).notNull(),
  type: text('type').notNull(), // 'question', 'bug', 'agent'
  content: text('content').notNull(),
  status: text('status').notNull().default('pending'), // 'pending', 'resolved', 'rejected'
  response: text('response'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const analytics = pgTable('analytics', {
  id: uuid('id').defaultRandom().primaryKey(),
  widgetId: uuid('widget_id').references(() => widget.id).notNull(),
  eventType: text('event_type').notNull(),
  eventData: json('event_data').notNull(),
  sessionId: text('session_id'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

export const subscription = pgTable('subscription', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => user.id).notNull(),
  plan: text('plan').notNull().default('free'), // 'free', 'pro'
  status: text('status').notNull().default('active'),
  currentPeriodStart: timestamp('current_period_start').notNull(),
  currentPeriodEnd: timestamp('current_period_end').notNull(),
  cancelAtPeriodEnd: boolean('cancel_at_period_end').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const sessions = pgTable('session', {
  sessionToken: text('sessionToken').primaryKey(),
  userId: uuid('userId').notNull().references(() => user.id, { onDelete: 'cascade' }),
  expires: timestamp('expires', { mode: 'date' }).notNull(),
});

export const verificationTokens = pgTable(
  'verificationToken',
  {
    identifier: text('identifier').notNull(),
    token: text('token').notNull(),
    expires: timestamp('expires', { mode: 'date' }).notNull(),
  },
  (vt) => ({
    compoundKey: primaryKey({ columns: [vt.identifier, vt.token] }),
  })
);

export const payment = pgTable('payment', {
  id: serial('id').primaryKey(),
  userId: uuid('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  subscriptionId: uuid('subscription_id').references(() => subscription.id),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: text('currency').notNull().default('USD'),
  status: text('status').notNull(), // 'succeeded', 'failed', 'pending'
  polarPaymentId: text('polar_payment_id').unique(),
  metadata: json('metadata'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

export const subscriptionUsage = pgTable('subscription_usage', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => user.id).notNull(),
  period: text('period').notNull(), // Format: 'YYYY-MM'
  messageCount: integer('message_count').default(0).notNull(),
  widgetCount: integer('widget_count').default(0).notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

export const testimonial = pgTable('testimonial', {
  id: uuid('id').defaultRandom().primaryKey(),
  quote: text('quote').notNull(),
  name: text('name').notNull(),
  designation: text('designation').notNull(),
  image: text('image').notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const faq = pgTable('faq', {
  id: uuid('id').defaultRandom().primaryKey(),
  question: text('question').notNull(),
  answer: text('answer').notNull(),
  category: text('category').notNull(), // 'general', 'pricing', 'technical', etc.
  order: integer('order').default(0).notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});