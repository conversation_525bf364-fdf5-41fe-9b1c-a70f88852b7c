import { CreditCard, Receipt, Calendar, Check } from 'lucide-react';

export default function BillingPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Billing</h1>
          <p className="text-text-secondary mt-1">
            Manage your subscription and billing details
          </p>
        </div>
        <button className="bg-accent-primary text-white px-4 py-2 rounded-lg hover:bg-accent-secondary transition-colors">
          Upgrade Plan
        </button>
      </div>

      {/* Current Plan */}
      <div className="bg-background-secondary rounded-lg border border-border-primary p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-lg font-semibold">Current Plan</h2>
            <p className="text-text-secondary mt-1">Pro Plan • $49/month</p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="px-3 py-1 bg-success/10 text-success rounded-full text-sm">
              Active
            </span>
            <button className="text-accent-primary text-sm">Change Plan</button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-lg bg-accent-primary/10 flex items-center justify-center">
              <Calendar className="w-6 h-6 text-accent-primary" />
            </div>
            <div>
              <p className="text-sm text-text-secondary">Next Billing Date</p>
              <p className="font-medium">March 15, 2024</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-lg bg-accent-primary/10 flex items-center justify-center">
              <CreditCard className="w-6 h-6 text-accent-primary" />
            </div>
            <div>
              <p className="text-sm text-text-secondary">Payment Method</p>
              <p className="font-medium">•••• 4242</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-lg bg-accent-primary/10 flex items-center justify-center">
              <Receipt className="w-6 h-6 text-accent-primary" />
            </div>
            <div>
              <p className="text-sm text-text-secondary">Billing Cycle</p>
              <p className="font-medium">Monthly</p>
            </div>
          </div>
        </div>
      </div>

      {/* Plan Features */}
      <div className="bg-background-secondary rounded-lg border border-border-primary p-6">
        <h2 className="text-lg font-semibold mb-6">Plan Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="font-medium">Included Features</h3>
            {[
              'Unlimited Conversations',
              'Advanced Analytics',
              'Custom Branding',
              'Priority Support',
              'API Access',
              'Team Collaboration',
            ].map((feature, index) => (
              <div key={index} className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-success" />
                <span>{feature}</span>
              </div>
            ))}
          </div>
          <div className="space-y-4">
            <h3 className="font-medium">Usage Limits</h3>
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-text-secondary">Conversations</span>
                  <span className="text-sm">8,234 / 10,000</span>
                </div>
                <div className="w-full bg-background-primary rounded-full h-2">
                  <div className="bg-accent-primary h-2 rounded-full" style={{ width: '82%' }} />
                </div>
              </div>
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-text-secondary">Team Members</span>
                  <span className="text-sm">8 / 10</span>
                </div>
                <div className="w-full bg-background-primary rounded-full h-2">
                  <div className="bg-accent-primary h-2 rounded-full" style={{ width: '80%' }} />
                </div>
              </div>
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-text-secondary">API Calls</span>
                  <span className="text-sm">45,678 / 50,000</span>
                </div>
                <div className="w-full bg-background-primary rounded-full h-2">
                  <div className="bg-accent-primary h-2 rounded-full" style={{ width: '91%' }} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment History */}
      <div className="bg-background-secondary rounded-lg border border-border-primary overflow-hidden">
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">Payment History</h2>
          <div className="space-y-4">
            {[
              {
                date: 'Feb 15, 2024',
                amount: '$49.00',
                status: 'Paid',
                invoice: '#INV-2024-02',
              },
              {
                date: 'Jan 15, 2024',
                amount: '$49.00',
                status: 'Paid',
                invoice: '#INV-2024-01',
              },
              {
                date: 'Dec 15, 2023',
                amount: '$49.00',
                status: 'Paid',
                invoice: '#INV-2023-12',
              },
            ].map((payment, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-4 bg-background-primary rounded-lg"
              >
                <div>
                  <p className="font-medium">{payment.date}</p>
                  <p className="text-sm text-text-secondary">{payment.invoice}</p>
                </div>
                <div className="flex items-center space-x-6">
                  <span className="font-medium">{payment.amount}</span>
                  <span className="px-3 py-1 bg-success/10 text-success rounded-full text-sm">
                    {payment.status}
                  </span>
                  <button className="text-accent-primary text-sm">Download</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
} 