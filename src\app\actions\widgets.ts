'use server';

import { db } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { revalidatePath } from 'next/cache';
import type { WidgetFormValues } from '@/lib/validations/widget';
import { authOptions } from '@/lib/auth';
import { and, eq } from 'drizzle-orm';
import { widget } from '@/db/schema';
import { v4 as uuidv4 } from 'uuid';

export async function createWidget(data: WidgetFormValues) {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
        throw new Error('Unauthorized');
    }

    const newWidget = await db.insert(widget).values({
        id: uuidv4(),
        ...data,
        userId: session.user.id,
        createdAt: new Date(),
        updatedAt: new Date()
    }).returning();

    revalidatePath('/dashboard/widgets');
    return newWidget[0];
}

export async function updateWidget(id: string, data: WidgetFormValues) {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
        throw new Error('Unauthorized');
    }

    const updatedWidget = await db.update(widget)
        .set({
            ...data,
            updatedAt: new Date()
        })
        .where(and(
            eq(widget.id, id),
            eq(widget.userId, session.user.id)
        ))
        .returning();

    revalidatePath('/dashboard/widgets');
    revalidatePath(`/dashboard/widgets/${id}`);
    return updatedWidget[0];
}

export async function deleteWidget(id: string) {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
        throw new Error('Unauthorized');
    }

    await db.delete(widget)
        .where(and(
            eq(widget.id, id),
            eq(widget.userId, session.user.id)
        ));

    revalidatePath('/dashboard/widgets');
}

function generateApiKey() {
    return 'sk_' + Array.from({ length: 32 }, () =>
        Math.floor(Math.random() * 16).toString(16)
    ).join('');
} 