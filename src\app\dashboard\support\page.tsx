"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/Card";
import { MessageCircle, Mail, Phone } from "lucide-react";

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  order: number;
}

export default function SupportPage() {
  const [faq, setfaq] = useState<FAQ[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchfaq = async () => {
      try {
        const response = await fetch('/api/faq?category=support');
        if (!response.ok) {
          throw new Error('Failed to fetch faq');
        }
        const data = await response.json();
        setfaq(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchfaq();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-500">{error}</p>
        <Button
          onClick={() => window.location.reload()}
          className="mt-4"
          variant="outline"
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-foreground">Support</h1>
        <p className="mt-2 text-muted-foreground">
          Get help with your BetterFAQ AI widget
        </p>
      </div>

      {/* Contact Options */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-primary/10 text-primary">
              <MessageCircle className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h3 className="font-medium">Live Chat</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Chat with our support team
              </p>
            </div>
          </div>
          <Button className="w-full mt-4">Start Chat</Button>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400">
              <Mail className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h3 className="font-medium">Email Support</h3>
              <p className="text-sm text-muted-foreground mt-1">
                <EMAIL>
              </p>
            </div>
          </div>
          <Button className="w-full mt-4" variant="outline">
            Send Email
          </Button>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400">
              <Phone className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h3 className="font-medium">Phone Support</h3>
              <p className="text-sm text-muted-foreground mt-1">
                +****************
              </p>
            </div>
          </div>
          <Button className="w-full mt-4" variant="outline">
            Call Now
          </Button>
        </Card>
      </div>

      {/* FAQ Section */}
      <div className="bg-background-secondary rounded-lg border border-border-primary p-6">
        <h2 className="text-lg font-semibold mb-6">Frequently Asked Questions</h2>
        <div className="space-y-4">
          {faq.map((faq) => (
            <div
              key={faq.id}
              className="bg-background-primary rounded-lg p-4"
            >
              <h3 className="font-medium mb-2">{faq.question}</h3>
              <p className="text-sm text-text-secondary">{faq.answer}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 