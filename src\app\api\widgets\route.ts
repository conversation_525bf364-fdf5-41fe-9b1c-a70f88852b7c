import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { widget } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { withUsageLimits, trackUsage } from '@/lib/middleware/usage';
import { ensureUserExists } from '@/lib/user-utils';

// GET - Fetch user's widgets
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure user exists in database
    await ensureUserExists({
      id: session.user.id,
      email: session.user.email || '',
      name: session.user.name || '',
      image: session.user.image || null,
    });

    const userWidgets = await db
      .select()
      .from(widget)
      .where(eq(widget.userId, session.user.id))
      .orderBy(widget.createdAt);

    return NextResponse.json({ widgets: userWidgets });

  } catch (error) {
    console.error('Error fetching widgets:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new widget (with usage checking)
export const POST = withUsageLimits({ action: 'widget' })(
  async function createWidget(request: NextRequest) {
    try {
      const session = await getServerSession(authOptions);
      const userId = session!.user!.id; // Safe because of middleware check

      // Ensure user exists in database
      await ensureUserExists({
        id: userId,
        email: session!.user!.email || '',
        name: session!.user!.name || '',
        image: session!.user!.image || null,
      });

      const body = await request.json();
      const {
        name,
        position = 'bottom-right',
        primaryColor = '#4F46E5',
        accentColor = '#ffffff',
        companyName,
        companyLogo,
        welcomeMessage,
        termsOfService,
        privacyPolicy,
        productDescription,
        features,
        pricing,
        commonQuestions = [],
        feedbackQuestion = 'How was your experience?',
      } = body;

      // Validate required fields
      if (!name) {
        return NextResponse.json(
          { error: 'Widget name is required' },
          { status: 400 }
        );
      }

      // Create the widget
      const newWidget = await db
        .insert(widget)
        .values({
          userId,
          name,
          position,
          primaryColor,
          accentColor,
          companyName,
          companyLogo,
          welcomeMessage,
          termsOfService,
          privacyPolicy,
          productDescription,
          features,
          pricing,
          commonQuestions,
          feedbackQuestion,
          isActive: true,
        })
        .returning();

      // Track the widget creation in usage
      await trackUsage(userId, 'widget');

      return NextResponse.json(
        {
          widget: newWidget[0],
          message: 'Widget created successfully'
        },
        { status: 201 }
      );

    } catch (error) {
      console.error('Error creating widget:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  }
);
