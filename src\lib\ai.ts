import { Groq } from 'groq-sdk';

if (!process.env.GROQ_API_KEY) {
    throw new Error('GROQ_API_KEY is not set');
}

export const groq = new Groq({
    apiKey: process.env.GROQ_API_KEY,
});

export const AI_MODEL = 'meta-llama/llama-4-maverick-17b-128e-instruct';

export async function generateAIResponse(
    messages: Array<{ role: 'user' | 'assistant'; content: string }>,
    context?: string
) {
    const completion = await groq.chat.completions.create({
        messages: [
            {
                role: 'system',
                content: `You are a helpful AI assistant for a product support widget. ${context ? `Here is some context about the product: ${context}` : ''
                    }`,
            },
            ...messages,
        ],
        model: AI_MODEL,
        temperature: 0.7,
        max_tokens: 1024,
    });

    return completion.choices[0]?.message?.content || 'Sorry, I could not generate a response.';
} 